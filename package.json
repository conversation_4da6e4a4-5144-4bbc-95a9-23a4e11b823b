{"name": "hrp", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "i": "pnpm install", "dev": "vite --mode dev", "ts:check": "vue-tsc --noEmit", "build:local": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build", "build:dev": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode dev", "build:test": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode test", "build:stage": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode stage", "build:prod": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode prod", "build:inner": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode inner", "build:outer": "node --max_old_space_size=10000 ./node_modules/vite/bin/vite.js build --mode outer", "serve:dev": "vite preview --mode dev", "serve:prod": "vite preview --mode prod", "preview": "pnpm build:local && vite preview", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --fix \"./src/**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c "}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.18.9", "@babel/plugin-transform-runtime": "^7.18.9", "@babel/preset-env": "^7.18.9", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.18.9", "@codemirror/autocomplete": "6.18.4", "@codemirror/lang-javascript": "6.2.2", "@codemirror/state": "6.5.0", "@codemirror/theme-one-dark": "6.1.2", "@codemirror/view": "^6.36.1", "@shopify/draggable": "1.0.0-beta.8", "@tailwindcss/postcss": "^4.1.8", "@types/exceljs": "^1.3.0", "@types/file-saver": "^2.0.5", "@types/node": "^20.16.1", "@types/sortablejs": "^1.15.8", "@types/vue": "^2.0.0", "@vicons/ionicons5": "^0.12.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.2.37", "autoprefixer": "^10.4.19", "babel-loader": "^8.2.5", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.5", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "browserslist": "^4.22.1", "caniuse-lite": "^1.0.30001697", "chalk": "^5.4.1", "code-inspector-plugin": "^0.20.10", "codemirror": "^6.0.1", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.27.2", "cross-env": "^7.0.3", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "^4.0.0", "dotenv": "^16.0.1", "friendly-errors-webpack-plugin": "^1.7.0", "happypack": "^5.0.1", "html-webpack-plugin": "^5.5.0", "increase-memory-limit": "^1.0.7", "less": "^4.1.3", "less-loader": "^11.1.0", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.6.1", "naive-ui": "^2.38.1", "pinyin": "^4.0.0-alpha.0", "postcss": "^8.5.4", "postcss-loader": "^7.0.1", "postcss-preset-env": "^7.7.2", "prettier": "2.8.7", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.53.0", "sass-loader": "^13.0.2", "segmentit": "^2.0.3", "style-loader": "^3.3.1", "stylelint": "^16.20.0", "stylelint-config-standard": "^38.0.0", "tailwindcss": "^4.1.8", "terser-webpack-plugin": "^5.3.7", "thread-loader": "^3.0.4", "ts-loader": "^9.4.2", "typescript": "^5.0.0", "unplugin-auto-import": "^0.6.9", "unplugin-vue-components": "^0.27.5", "unplugin-vue-define-options": "^1.5.2", "vfonts": "0.0.3", "vite": "^5.4.11", "vite-plugin-compression": "^0.5.1", "vite-plugin-dts": "^3.9.1", "vite-plugin-html": "^3.2.2", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-inspector": "^5.1.3", "vue-codemirror": "^6.1.1", "vue-draggable-next": "2.1.1", "vue-loader": "^17.0.0", "webpack": "5.76.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.7.4", "webpack-merge": "^5.8.0"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/hierarchy": "^0.6.11", "@antv/layout": "^0.3.20", "@babel/polyfill": "^7.12.1", "@codemirror/commands": "6.7.1", "@codemirror/lang-json": "6.0.1", "@codemirror/language": "6.10.8", "@codemirror/lint": "6.8.4", "@codemirror/search": "6.5.8", "@dagrejs/dagre": "^1.1.4", "@element-plus/icons-vue": "^2.1.0", "@form-create/designer": "3.2.10", "@form-create/element-ui": "3.2.18", "@form-create/naive-ui": "3.2.18", "@huyuchen/editor": "1.0.7", "@lezer/common": "^1.2.3", "@logicflow/core": "^1.2.1", "@logicflow/extension": "^1.2.1", "@sangtian152/vue3-sign": "0.0.6", "@tailwindcss/vite": "^4.1.8", "@types/lodash": "^4.17.5", "@vue-flow/core": "^1.42.1", "@vue-office/excel": "^1.7.11", "@vue/cli-plugin-babel": "^5.0.8", "@vue/reactivity": "^3.4.38", "@vue/runtime-core": "^3.5.8", "@vueuse/core": "^9.13.0", "@vueuse/router": "^12.2.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ant-design-vue": "^4.0.8", "axios": "^0.27.2", "big.js": "^6.2.2", "bpmn-js-token-simulation": "0.10.0", "browser-md5-file": "^1.1.1", "camunda-bpmn-moddle": "^7.0.1", "codemirror-json-schema": "0.7.9", "compressorjs": "^1.2.1", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "diagram-js": "^14.9.0", "docx-preview": "^0.1.16", "dom-to-code": "^1.5.4", "echarts": "^5.5.0", "element-plus": "2.7.0", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "js-calendar-converter": "^0.0.6", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mathjs": "^12.2.0", "min-dash": "^4.1.1", "mitt": "^3.0.1", "moment": "^2.30.1", "monaco-editor": "0.52.2", "pinia": "^2.0.31", "pinia-plugin-persist": "^1.0.0", "qs": "^6.13.0", "sass": "^1.77.5", "sass-loader": "^14.2.1", "screenfull": "^6.0.2", "seemly": "^0.3.8", "sortablejs": "^1.15.2", "steady-xml": "^0.1.0", "test2": "file:../sfm_web", "vite-plugin-progress": "^0.0.7", "vue": "^3.5.8", "vue-demi": "0.14.6", "vue-echarts": "^7.0.3", "vue-print-next": "^1.0.6", "vue-router": "^4.3.2", "vue-tsc": "^2.0.6", "vue-types": "^5.1.1", "vue3-json-editor": "^1.1.5", "vuex": "^4.0.2"}, "browserslist": ["Chrome 60"], "pnpm": {"onlyBuiltDependencies": ["@vue-office/excel", "core-js", "core-js-pure", "es5-ext", "esbuild", "node-sass", "vue-demi"]}}